/**
 * Guidant Mastra AI Research Engine
 *
 * Single workflow engine that orchestrates autonomous research tasks
 * using specialized internal workflows and external MCP servers.
 */

import { <PERSON><PERSON> } from '@mastra/core';
import { PinoLogger } from '@mastra/loggers';
// import { MCPClient } from '@mastra/mcp'; // Temporarily disabled
import { createServer } from './server.js';
import { researchOrchestrator } from './workflows/orchestrator.js';
import { technicalDocumentationWorkflow } from './workflows/technology.js';
import { marketResearchWorkflow } from './workflows/market.js';
import { uxResearchWorkflow } from './workflows/ux.js';
import { loadConfig } from './utils/config.js';

// Create MCP Client for external tool integration
// Temporarily disabled for testing - will re-enable after fixing package names
const mcpClient: any = null; // new MCPClient({
//   id: 'guidant-research-mcp',
//   servers: {
//     context7: {
//       command: 'node',
//       args: ['node_modules/@upstash/context7-mcp/dist/index.js'], // Direct execution
//       env: {
//         // Context7 environment variables would go here
//         NODE_ENV: process.env['NODE_ENV'] || 'development'
//       },
//       timeout: 30000,
//       enableServerLogs: true
//     },
//     tavily: {
//       command: 'npx',
//       args: ['-y', '--package=tavily-mcp', 'tavily-mcp'],
//       env: {
//         TAVILY_API_KEY: process.env.TAVILY_API_KEY || '', // Explicitly pass from process.env
//         NODE_ENV: process.env['NODE_ENV'] || 'development'
//       },
//       timeout: 30000,
//       enableServerLogs: true
//     },
//     // Note: Stagehand MCP server temporarily disabled - package name needs verification
//     // stagehand: {
//     //   command: 'npx',
//     //   args: ['-y', '--package=stagehand-mcp', 'stagehand-mcp'],
//     //   env: {
//     //     NODE_ENV: process.env['NODE_ENV'] || 'development'
//     //   },
//     //   timeout: 60000, // Browser automation may take longer
//     //   enableServerLogs: true
//     // }
//   },
//   timeout: 30000
// });

// Create Mastra instance for export
const config = loadConfig();
const mastra = new Mastra({
  workflows: {
    researchOrchestrator,
    technicalDocumentationWorkflow,
    marketResearchWorkflow,
    uxResearchWorkflow
  },
  logger: new PinoLogger({
    name: 'Mastra Research',
    level: 'info'
  })
});

// Make MCPClient available to workflows by adding it to the mastra instance
// This is a workaround since Mastra doesn't directly support MCPClient in constructor
// Temporarily disabled: (mastra as any).mcpClient = mcpClient;

async function main() {
  console.log('Initializing Guidant Mastra AI Research Engine...');

  try {
    // Initialize MCP Client and get available tools (with graceful fallback)
    console.log('MCP Client temporarily disabled for testing...');
    let mcpTools = {};
    if (mcpClient) {
      try {
        console.log('Getting MCP tools...');
        mcpTools = await mcpClient.getTools();
        console.log('MCP Tools available:', Object.keys(mcpTools));
      } catch (mcpError) {
        console.warn('MCP Client initialization failed, workflows will use fallback mode:', mcpError instanceof Error ? mcpError.message : String(mcpError));
        // Continue without MCP tools - workflows have fallback implementations
      }
    } else {
      console.log('MCP Client disabled - running in fallback mode');
    }

    console.log('About to create Mastra server...');
    console.log('Mastra instance:', !!mastra);
    console.log('Config:', !!config);

    // Create and start the server
    const server = createServer(mastra, config);
    console.log('Server instance created');
    const port = config.server.port || 8080;
    console.log('Server created, starting on port', port);

    // Set up graceful shutdown handlers before starting server
    process.on('SIGTERM', async () => {
      console.log('Received SIGTERM, shutting down gracefully...');
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      console.log('Received SIGINT, shutting down gracefully...');
      process.exit(0);
    });

    server.listen(port, (err?: Error) => {
      if (err) {
        console.error('Failed to start server:', err);
        process.exit(1);
      } else {
        console.log(`Guidant Mastra AI Research Engine started on port ${port}`);
        const workflows = mastra.getWorkflows();
        console.log('Available workflows:', Object.keys(workflows || {}));
        console.log('Available MCP tools:', Object.keys(mcpTools));
        if (Object.keys(mcpTools).length === 0) {
          console.log('Note: Running in fallback mode - MCP tools not available');
        }
        console.log('Server is ready to accept connections');
      }
    });

  } catch (error) {
    console.error('Failed to initialize Mastra AI Research Engine:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit the process, just log the error
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Start the application
main().catch((error) => {
  console.error('Unhandled error during startup:', error);
  process.exit(1);
});

// Export the mastra instance and MCP client for external use
export { mastra as default, mcpClient };

// Export integration utilities
export { createGuidantIntegration, GuidantMastraIntegration } from './integration/index.js';

// Export types for use in other modules
export type { Config } from './utils/config';
