/**
 * Guidant Mastra AI Research Engine
 *
 * Single workflow engine that orchestrates autonomous research tasks
 * using specialized internal workflows and external MCP servers.
 */

import { <PERSON><PERSON> } from '@mastra/core';
import { PinoLogger } from '@mastra/loggers';
// import { MCPClient } from '@mastra/mcp'; // Temporarily disabled
import { createServer } from './server.js';
import { researchOrchestrator } from './workflows/orchestrator.js';
import { technicalDocumentationWorkflow } from './workflows/technology.js';
import { marketResearchWorkflow } from './workflows/market.js';
import { uxResearchWorkflow } from './workflows/ux.js';
import { loadConfig } from './utils/config.js';

// Create MCP Client for external tool integration
// Temporarily disabled for testing - will re-enable after fixing package names
const mcpClient: any = null; // new MCPClient({
//   id: 'guidant-research-mcp',
//   servers: {
//     context7: {
//       command: 'node',
//       args: ['node_modules/@upstash/context7-mcp/dist/index.js'], // Direct execution
//       env: {
//         // Context7 environment variables would go here
//         NODE_ENV: process.env['NODE_ENV'] || 'development'
//       },
//       timeout: 30000,
//       enableServerLogs: true
//     },
//     tavily: {
//       command: 'npx',
//       args: ['-y', '--package=tavily-mcp', 'tavily-mcp'],
//       env: {
//         TAVILY_API_KEY: process.env.TAVILY_API_KEY || '', // Explicitly pass from process.env
//         NODE_ENV: process.env['NODE_ENV'] || 'development'
//       },
//       timeout: 30000,
//       enableServerLogs: true
//     },
//     // Note: Stagehand MCP server temporarily disabled - package name needs verification
//     // stagehand: {
//     //   command: 'npx',
//     //   args: ['-y', '--package=stagehand-mcp', 'stagehand-mcp'],
//     //   env: {
//     //     NODE_ENV: process.env['NODE_ENV'] || 'development'
//     //   },
//     //   timeout: 60000, // Browser automation may take longer
//     //   enableServerLogs: true
//     // }
//   },
//   timeout: 30000
// });

// Create Mastra instance for export
const config = loadConfig();
const mastra = new Mastra({
  workflows: {
    researchOrchestrator,
    technicalDocumentationWorkflow,
    marketResearchWorkflow,
    uxResearchWorkflow
  },
  logger: new PinoLogger({
    name: 'Mastra Research',
    level: 'info'
  })
});

// Make MCPClient available to workflows by adding it to the mastra instance
// This is a workaround since Mastra doesn't directly support MCPClient in constructor
// Temporarily disabled: (mastra as any).mcpClient = mcpClient;

async function main() {
  console.log('=== STARTUP TRACE: Initializing Guidant Mastra AI Research Engine ===');

  try {
    // Initialize MCP Client and get available tools (with graceful fallback)
    console.log('STEP 1: MCP Client initialization (disabled for testing)');
    let mcpTools = {};
    if (mcpClient) {
      try {
        console.log('STEP 1a: Getting MCP tools...');
        mcpTools = await mcpClient.getTools();
        console.log('STEP 1b: MCP Tools available:', Object.keys(mcpTools));
      } catch (mcpError) {
        console.warn('STEP 1c: MCP Client initialization failed, workflows will use fallback mode:', mcpError instanceof Error ? mcpError.message : String(mcpError));
        // Continue without MCP tools - workflows have fallback implementations
      }
    } else {
      console.log('STEP 1d: MCP Client disabled - running in fallback mode');
    }

    console.log('STEP 2: Validating Mastra instance and config');
    console.log('STEP 2a: Mastra instance exists:', !!mastra);
    console.log('STEP 2b: Config exists:', !!config);
    console.log('STEP 2c: Available workflows:', Object.keys(mastra.getWorkflows() || {}));

    console.log('STEP 3: Creating Express server');
    const server = createServer(mastra, config);
    console.log('STEP 3a: Server instance created successfully');

    const port = config.server.port || 8080;
    console.log('STEP 4: Preparing to start server on port', port);

    console.log('STEP 5: Setting up graceful shutdown handlers');
    // Set up graceful shutdown handlers before starting server
    process.on('SIGTERM', async () => {
      console.log('Received SIGTERM, shutting down gracefully...');
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      console.log('Received SIGINT, shutting down gracefully...');
      process.exit(0);
    });

    console.log('STEP 6: Starting server.listen() - THIS IS WHERE HANGS OFTEN OCCUR');
    const host = config.server.host || 'localhost';
    console.log(`STEP 6a: Binding to host: ${host}, port: ${port}`);

    server.listen(port, host, (err?: Error) => {
      if (err) {
        console.error('STEP 6b: Server listen FAILED:', err);
        process.exit(1);
      } else {
        console.log('STEP 6c: Server listen SUCCESS');
        console.log(`=== SERVER STARTED: Guidant Mastra AI Research Engine on ${host}:${port} ===`);
        const workflows = mastra.getWorkflows();
        console.log('Available workflows:', Object.keys(workflows || {}));
        console.log('Available MCP tools:', Object.keys(mcpTools));
        if (Object.keys(mcpTools).length === 0) {
          console.log('Note: Running in fallback mode - MCP tools not available');
        }
        console.log(`=== SERVER READY: http://${host}:${port}/health ===`);

        // Keep the process alive and log periodic status
        setInterval(() => {
          console.log(`[${new Date().toISOString()}] Server still running on ${host}:${port}`);
        }, 30000); // Log every 30 seconds
      }
    });

  } catch (error) {
    console.error('Failed to initialize Mastra AI Research Engine:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('=== UNHANDLED PROMISE REJECTION DETECTED ===');
  console.error('Promise:', promise);
  console.error('Reason:', reason);
  console.error('Stack trace:', reason instanceof Error ? reason.stack : 'No stack trace available');
  console.error('=== THIS MAY BE CAUSING SERVER TERMINATION ===');
  // Don't exit the process, just log the error
});

process.on('uncaughtException', (error) => {
  console.error('=== UNCAUGHT EXCEPTION DETECTED ===');
  console.error('Error:', error);
  console.error('Stack trace:', error.stack);
  console.error('=== PROCESS WILL EXIT ===');
  process.exit(1);
});

// Start the application
main().catch((error) => {
  console.error('Unhandled error during startup:', error);
  process.exit(1);
});

// Export the mastra instance and MCP client for external use
export { mastra as default, mcpClient };

// Export integration utilities
export { createGuidantIntegration, GuidantMastraIntegration } from './integration/index.js';

// Export types for use in other modules
export type { Config } from './utils/config';
